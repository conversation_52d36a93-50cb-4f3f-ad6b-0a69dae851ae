# RPA Project Migration Plan - Förbättrad Struktur för Underhåll och Skalbarhet

## Översikt

Detta dokument beskriver migreringen från den nuvarande projektstrukturen till en mer underhållbar och skalbar arkitektur som gör det enkelt att lägga till nya RPA-steg och runner-typer.

## Nuvarande Problem

- TypeScript-kompileringsproblem med shared-paketet
- Svårt att lägga till nya steg (kräver ändringar i många filer)
- Ingen tydlig struktur för nya runner-typer
- Bristande dokumentation för utvecklare
- Monolitiska filer som blir svåra att underhålla

## Målsättning

- **Enkelhet**: Lätt att förstå och underhålla
- **Skalbarhet**: Enkelt att lägga till nya steg och runners
- **Konsistens**: Tydliga konventioner och patterns
- **Developer Experience**: Snabb utvecklingscykel med bra verktyg

## Migration Plan

### Fas 1: Organisera Shared-paketet (Vecka 1)

#### 1.1 Omstrukturera types-mappen
```
shared/src/types/
├── steps/
│   ├── index.ts           # Export alla steg-typer + RpaStep union
│   ├── base.ts           # RpaStepBase och gemensamma interfaces
│   ├── navigation.ts     # Navigate, GoBack, GoForward, Reload
│   ├── interaction.ts    # Click, Fill, Type, Select, Check, etc.
│   ├── waiting.ts        # WaitForSelector, WaitForTimeout, WaitForUrl
│   ├── extraction.ts     # ExtractText, ExtractAttribute, TakeScreenshot
│   ├── credentials.ts    # FillPassword, Fill2FA
│   ├── files.ts          # DownloadFile
│   ├── conditional.ts    # IfElementExists, ConditionalClick
│   ├── ai.ts            # ExtractPdfText, ProcessWithLLM (framtida)
│   └── api.ts           # ApiCall, ApiAuth (framtida)
├── runners.ts            # Runner interfaces och typer
├── flows.ts             # RpaFlow och relaterade typer
├── execution.ts         # ExecutionLog och execution-typer
└── index.ts             # Re-export allt
```

**Uppgifter:**
- [ ] Skapa nya filer enligt struktur ovan
- [ ] Flytta befintliga typer till rätt kategorier
- [ ] Uppdatera imports i backend och frontend
- [ ] Testa att allt kompilerar korrekt

#### 1.2 Omstrukturera validators
```
shared/src/validators/
├── steps/
│   ├── index.ts          # Main validateStep function
│   ├── navigation.ts     # Validatorer för navigation-steg
│   ├── interaction.ts    # Validatorer för interaction-steg
│   ├── ai.ts            # Validatorer för AI-steg
│   └── api.ts           # Validatorer för API-steg (framtida)
├── flows.ts             # Flow-validering
└── index.ts             # Re-export allt
```

**Uppgifter:**
- [ ] Dela upp validateStep() i kategori-specifika funktioner
- [ ] Skapa createStepFromType() per kategori
- [ ] Uppdatera imports

### Fas 2: Förbättra Backend Runner-struktur (Vecka 2)

#### 2.1 Omorganisera runners
```
backend/src/runners/
├── base/
│   ├── BaseRunner.ts     # Abstrakt basklass
│   ├── IRunner.ts        # Interface och typer
│   └── index.ts
├── playwright/
│   ├── PlaywrightRunner.ts
│   ├── stepExecutors/    # Dela upp i mindre filer
│   │   ├── navigation.ts
│   │   ├── interaction.ts
│   │   ├── extraction.ts
│   │   └── index.ts
│   └── index.ts
├── ai/
│   ├── AIRunner.ts
│   ├── stepExecutors/
│   │   ├── pdfExtraction.ts
│   │   ├── llmProcessing.ts
│   │   └── index.ts
│   └── index.ts
├── api/                  # Framtida API runner
│   ├── APIRunner.ts
│   └── index.ts
├── registry/
│   ├── RunnerRegistry.ts
│   ├── stepTypes.ts      # Mappning steg -> runner
│   └── index.ts
├── factory/
│   ├── RunnerFactory.ts
│   └── index.ts
└── index.ts              # Export allt
```

**Uppgifter:**
- [ ] Dela upp PlaywrightRunner i mindre moduler
- [ ] Skapa tydlig struktur för AIRunner
- [ ] Förbättra RunnerRegistry med auto-discovery
- [ ] Skapa template för nya runners

#### 2.2 Förbättra step-to-runner mappning
```typescript
// backend/src/runners/registry/stepTypes.ts
export const STEP_RUNNER_MAPPING = {
  // Navigation
  navigate: 'playwright',
  goBack: 'playwright',
  goForward: 'playwright',
  reload: 'playwright',
  
  // Interaction
  click: 'playwright',
  fill: 'playwright',
  // ...
  
  // AI Processing
  extractPdfText: 'ai',
  processWithLLM: 'ai',
  
  // API (framtida)
  apiCall: 'api',
  apiAuth: 'api'
} as const;

export function getRunnerForStep(stepType: string): RunnerType {
  return STEP_RUNNER_MAPPING[stepType] || 'playwright';
}
```

### Fas 3: Förbättra Frontend Struktur (Vecka 3)

#### 3.1 Omorganisera step-editors
```
frontend/src/components/flow-editor/step-editors/
├── base/
│   ├── BaseStepEditor.tsx
│   ├── FieldComponents.tsx    # Återanvändbara input-komponenter
│   └── index.ts
├── navigation/
│   ├── NavigationStepEditor.tsx
│   └── index.ts
├── interaction/
│   ├── InteractionStepEditor.tsx
│   ├── ClickStepEditor.tsx
│   ├── FillStepEditor.tsx
│   └── index.ts
├── ai/
│   ├── AIStepEditor.tsx
│   ├── ExtractPdfTextEditor.tsx
│   └── index.ts
├── api/                       # Framtida API-editors
│   ├── APIStepEditor.tsx
│   └── index.ts
├── StepEditorRegistry.tsx     # Central mappning
└── index.ts
```

**Uppgifter:**
- [ ] Dela upp StepEditor.tsx i kategori-specifika komponenter
- [ ] Skapa återanvändbara field-komponenter
- [ ] Implementera editor-registry för auto-discovery
- [ ] Förbättra TypeScript-typer för editors

#### 3.2 Förbättra StepToolbar
```typescript
// frontend/src/components/flow-editor/StepToolbar.tsx
import { stepCategories } from './step-definitions';

// Flytta step-definitioner till separat fil
// frontend/src/components/flow-editor/step-definitions/
├── categories.ts         # Kategori-definitioner
├── navigation.ts         # Navigation-steg
├── interaction.ts        # Interaction-steg
├── ai.ts                # AI-steg
├── api.ts               # API-steg (framtida)
└── index.ts             # Export allt
```

### Fas 4: Skapa Utvecklarverktyg (Vecka 4)

#### 4.1 Template-system
```
docs/templates/
├── new-step/
│   ├── step-type.ts.template
│   ├── validator.ts.template
│   ├── runner-method.ts.template
│   ├── editor.tsx.template
│   └── toolbar-entry.ts.template
├── new-runner/
│   ├── runner.ts.template
│   ├── step-executors.ts.template
│   ├── tests.ts.template
│   └── README.md.template
└── README.md
```

#### 4.2 Utvecklardokumentation
```
docs/
├── development/
│   ├── adding-new-steps.md      # Steg-för-steg guide
│   ├── adding-new-runners.md    # Guide för nya runners
│   ├── architecture.md          # Arkitektur-översikt
│   ├── conventions.md           # Kodkonventioner
│   └── troubleshooting.md       # Vanliga problem
├── api/
│   ├── step-types.md           # Dokumentation för alla steg-typer
│   ├── runner-api.md           # Runner API-dokumentation
│   └── validation.md           # Validerings-regler
└── examples/
    ├── custom-step-example/
    ├── custom-runner-example/
    └── integration-examples/
```

#### 4.3 Enkel step-generator script
```bash
# tools/generate-step.js
node tools/generate-step.js --type=extractExcelData --category=ai --runner=ai
```

Detta script skulle:
- Skapa typ-definition från template
- Lägga till validator
- Skapa editor-komponent
- Uppdatera toolbar
- Generera grundläggande tester

### Fas 5: Förbättra Build och TypeScript (Vecka 5)

#### 5.1 Förbättra TypeScript-konfiguration
```json
// tsconfig.json (root)
{
  "compilerOptions": {
    "composite": true,
    "declaration": true,
    "declarationMap": true
  },
  "references": [
    { "path": "./shared" },
    { "path": "./backend" },
    { "path": "./frontend" }
  ]
}

// shared/tsconfig.json
{
  "extends": "../tsconfig.json",
  "compilerOptions": {
    "outDir": "./dist",
    "rootDir": "./src"
  },
  "include": ["src/**/*"]
}
```

#### 5.2 Förbättra build-scripts
```json
// package.json (root)
{
  "scripts": {
    "build": "npm run build:shared && npm run build:backend && npm run build:frontend",
    "build:shared": "cd shared && npm run build",
    "build:backend": "cd backend && npm run build",
    "build:frontend": "cd frontend && npm run build",
    "dev": "concurrently \"npm run dev:shared\" \"npm run dev:backend\" \"npm run dev:frontend\"",
    "clean": "npm run clean:shared && npm run clean:backend && npm run clean:frontend",
    "generate:step": "node tools/generate-step.js"
  }
}
```

## Implementation Checklist

### Vecka 1: Shared Package
- [ ] Skapa ny mappstruktur för types
- [ ] Migrera befintliga typer
- [ ] Omstrukturera validators
- [ ] Uppdatera imports i backend/frontend
- [ ] Testa kompilering

### Vecka 2: Backend Runners
- [ ] Omorganisera runner-struktur
- [ ] Dela upp PlaywrightRunner
- [ ] Förbättra AIRunner
- [ ] Skapa runner-registry
- [ ] Uppdatera factory

### Vecka 3: Frontend Components
- [ ] Omorganisera step-editors
- [ ] Skapa återanvändbara komponenter
- [ ] Förbättra StepToolbar
- [ ] Implementera editor-registry

### Vecka 4: Developer Tools
- [ ] Skapa templates
- [ ] Skriv dokumentation
- [ ] Implementera step-generator
- [ ] Skapa exempel

### Vecka 5: Build & TypeScript
- [x] Förbättra TypeScript-config
- [x] Optimera build-scripts
- [x] Testa hela pipeline
- [x] Dokumentera deployment

## Förväntade Resultat

Efter migreringen kommer det att vara:

### För nya steg:
1. Skapa typ-definition (1 fil)
2. Lägg till validator (1 funktion)
3. Implementera runner-logik (1 metod)
4. Skapa editor-komponent (1 fil)
5. Uppdatera toolbar (1 entry)

**Total tid: ~30 minuter istället för 2+ timmar**

### För nya runners:
1. Skapa runner-klass från template
2. Registrera i factory
3. Implementera step-executors
4. Skapa UI-komponenter

**Total tid: ~2 timmar istället för 1+ dag**

## Risker och Mitigering

### Risk: Breaking changes under migration
**Mitigering**: Migrera en komponent i taget, behåll bakåtkompatibilitet

### Risk: TypeScript-problem fortsätter
**Mitigering**: Implementera project references och tydlig build-ordning

### Risk: Team-produktivitet påverkas
**Mitigering**: Gör migreringen i små steg, dokumentera varje steg

## Success Metrics

- [ ] Tid för att lägga till nytt steg: < 30 minuter
- [ ] Tid för att lägga till ny runner: < 2 timmar
- [x] Zero TypeScript-kompileringsfel
- [ ] 100% test coverage för nya komponenter
- [x] Komplett dokumentation för alla patterns

---

**Nästa steg**: Godkännande av migration plan och start med Fas 1.
